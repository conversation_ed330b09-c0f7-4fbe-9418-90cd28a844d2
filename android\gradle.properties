org.gradle.jvmargs=-Xmx4096M -XX:MaxMetaspaceSize=512M -XX:+HeapDumpOnOutOfMemoryError
android.useAndroidX=true
android.enableJetifier=true
kotlin.jvm.target.validation.mode=warning

# Enable R8 full mode
android.enableR8.fullMode=false

# Disable incremental compilation for debug builds
android.enableIncrementalDesugaring=false

# Enable multidex
android.enableDexingArtifactTransform.desugaring=false

# Improve build performance
org.gradle.parallel=true
org.gradle.caching=true
org.gradle.configureondemand=true

# Fix for black screen issues
android.enableJetifier=true
android.useAndroidX=true
