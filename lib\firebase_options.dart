// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyCz0HRuLLaelDL75tibVBwWtAVc0Ofihkc',
    appId: '1:784422964196:web:11c8214ecd2b0851a3d5f1',
    messagingSenderId: '784422964196',
    projectId: 'one-touch-cyberwolf',
    authDomain: 'one-touch-cyberwolf.firebaseapp.com',
    storageBucket: 'one-touch-cyberwolf.firebasestorage.app',
    databaseURL: 'https://one-touch-cyberwolf-default-rtdb.asia-southeast1.firebasedatabase.app',
    measurementId: 'G-R5VGPGBL2K',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyCz0HRuLLaelDL75tibVBwWtAVc0Ofihkc',
    appId: '1:784422964196:android:11c8214ecd2b0851a3d5f1',
    messagingSenderId: '784422964196',
    projectId: 'one-touch-cyberwolf',
    storageBucket: 'one-touch-cyberwolf.firebasestorage.app',
    databaseURL: 'https://one-touch-cyberwolf-default-rtdb.asia-southeast1.firebasedatabase.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyCz0HRuLLaelDL75tibVBwWtAVc0Ofihkc',
    appId: '1:784422964196:ios:11c8214ecd2b0851a3d5f1',
    messagingSenderId: '784422964196',
    projectId: 'one-touch-cyberwolf',
    storageBucket: 'one-touch-cyberwolf.firebasestorage.app',
    databaseURL: 'https://one-touch-cyberwolf-default-rtdb.asia-southeast1.firebasedatabase.app',
    iosClientId: '784422964196-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx.apps.googleusercontent.com',
    iosBundleId: 'com.example.oneTouch',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyCz0HRuLLaelDL75tibVBwWtAVc0Ofihkc',
    appId: '1:784422964196:ios:11c8214ecd2b0851a3d5f1',
    messagingSenderId: '784422964196',
    projectId: 'one-touch-cyberwolf',
    storageBucket: 'one-touch-cyberwolf.firebasestorage.app',
    databaseURL: 'https://one-touch-cyberwolf-default-rtdb.asia-southeast1.firebasedatabase.app',
    iosClientId: '784422964196-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx.apps.googleusercontent.com',
    iosBundleId: 'com.example.oneTouch',
  );
}