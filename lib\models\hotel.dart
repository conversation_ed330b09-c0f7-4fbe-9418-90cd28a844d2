import 'package:cloud_firestore/cloud_firestore.dart';

class Hotel {
  final String id;
  final String name;
  final String description;
  final String address;
  final String city;
  final String country;
  final double latitude;
  final double longitude;
  final List<String> images;
  final double rating;
  final int reviewCount;
  final List<String> amenities;
  final double pricePerNight;
  final String currency;
  final bool isAvailable;
  final DateTime createdAt;
  final DateTime updatedAt;

  Hotel({
    required this.id,
    required this.name,
    required this.description,
    required this.address,
    required this.city,
    required this.country,
    required this.latitude,
    required this.longitude,
    required this.images,
    required this.rating,
    required this.reviewCount,
    required this.amenities,
    required this.pricePerNight,
    required this.currency,
    required this.isAvailable,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Hotel.fromFirestore(DocumentSnapshot doc) {
    Map<String, dynamic> data = doc.data() as Map<String, dynamic>;
    
    return Hotel(
      id: doc.id,
      name: data['name'] ?? '',
      description: data['description'] ?? '',
      address: data['address'] ?? '',
      city: data['city'] ?? '',
      country: data['country'] ?? '',
      latitude: (data['latitude'] ?? 0.0).toDouble(),
      longitude: (data['longitude'] ?? 0.0).toDouble(),
      images: List<String>.from(data['images'] ?? []),
      rating: (data['rating'] ?? 0.0).toDouble(),
      reviewCount: data['reviewCount'] ?? 0,
      amenities: List<String>.from(data['amenities'] ?? []),
      pricePerNight: (data['pricePerNight'] ?? 0.0).toDouble(),
      currency: data['currency'] ?? 'USD',
      isAvailable: data['isAvailable'] ?? true,
      createdAt: (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (data['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'name': name,
      'description': description,
      'address': address,
      'city': city,
      'country': country,
      'latitude': latitude,
      'longitude': longitude,
      'images': images,
      'rating': rating,
      'reviewCount': reviewCount,
      'amenities': amenities,
      'pricePerNight': pricePerNight,
      'currency': currency,
      'isAvailable': isAvailable,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
    };
  }

  Hotel copyWith({
    String? id,
    String? name,
    String? description,
    String? address,
    String? city,
    String? country,
    double? latitude,
    double? longitude,
    List<String>? images,
    double? rating,
    int? reviewCount,
    List<String>? amenities,
    double? pricePerNight,
    String? currency,
    bool? isAvailable,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Hotel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      address: address ?? this.address,
      city: city ?? this.city,
      country: country ?? this.country,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      images: images ?? this.images,
      rating: rating ?? this.rating,
      reviewCount: reviewCount ?? this.reviewCount,
      amenities: amenities ?? this.amenities,
      pricePerNight: pricePerNight ?? this.pricePerNight,
      currency: currency ?? this.currency,
      isAvailable: isAvailable ?? this.isAvailable,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

class Room {
  final String id;
  final String hotelId;
  final String name;
  final String description;
  final String type;
  final List<String> images;
  final double pricePerNight;
  final String currency;
  final int maxOccupancy;
  final List<String> amenities;
  final bool isAvailable;
  final int availableRooms;

  Room({
    required this.id,
    required this.hotelId,
    required this.name,
    required this.description,
    required this.type,
    required this.images,
    required this.pricePerNight,
    required this.currency,
    required this.maxOccupancy,
    required this.amenities,
    required this.isAvailable,
    required this.availableRooms,
  });

  factory Room.fromFirestore(DocumentSnapshot doc) {
    Map<String, dynamic> data = doc.data() as Map<String, dynamic>;
    
    return Room(
      id: doc.id,
      hotelId: data['hotelId'] ?? '',
      name: data['name'] ?? '',
      description: data['description'] ?? '',
      type: data['type'] ?? '',
      images: List<String>.from(data['images'] ?? []),
      pricePerNight: (data['pricePerNight'] ?? 0.0).toDouble(),
      currency: data['currency'] ?? 'USD',
      maxOccupancy: data['maxOccupancy'] ?? 1,
      amenities: List<String>.from(data['amenities'] ?? []),
      isAvailable: data['isAvailable'] ?? true,
      availableRooms: data['availableRooms'] ?? 0,
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'hotelId': hotelId,
      'name': name,
      'description': description,
      'type': type,
      'images': images,
      'pricePerNight': pricePerNight,
      'currency': currency,
      'maxOccupancy': maxOccupancy,
      'amenities': amenities,
      'isAvailable': isAvailable,
      'availableRooms': availableRooms,
    };
  }
}
