import 'package:intl/intl.dart';

class EmailService {
  // EmailJS Configuration
  // Set up your EmailJS account and update these values
  static const String _serviceId = 'service_one_touch_hotel'; // Your EmailJS service ID
  static const String _bookingTemplateId = 'template_booking_confirmation'; // Booking confirmation template
  static const String _paymentSuccessTemplateId = 'template_payment_success'; // Payment success template
  static const String _paymentFailedTemplateId = 'template_payment_failed'; // Payment failed template
  static const String _cancellationTemplateId = 'template_booking_cancellation'; // Cancellation template
  static const String _publicKey = 'YOUR_EMAILJS_PUBLIC_KEY'; // Replace with your public key
  static const String _privateKey = 'YOUR_EMAILJS_PRIVATE_KEY'; // Replace with your private key

  // Initialize EmailJS (Simulated)
  Future<void> initializeEmailJS() async {
    try {
      // Simulate initialization
      await Future.delayed(const Duration(milliseconds: 200));
      print('EmailJS initialized successfully (Demo Mode)');
    } catch (e) {
      print('Error initializing EmailJS: $e');
    }
  }

  // Send booking confirmation email
  Future<bool> sendBookingConfirmation({
    required String toEmail,
    required String customerName,
    required String bookingId,
    required String hotelName,
    required String roomName,
    required DateTime checkInDate,
    required DateTime checkOutDate,
    required double amount,
  }) async {
    try {
      await initializeEmailJS();

      Map<String, dynamic> templateParams = {
        'to_email': toEmail,
        'customer_name': customerName,
        'booking_id': bookingId,
        'hotel_name': hotelName,
        'room_name': roomName,
        'check_in_date': DateFormat('dd MMM yyyy').format(checkInDate),
        'check_out_date': DateFormat('dd MMM yyyy').format(checkOutDate),
        'amount': '₹${amount.toStringAsFixed(2)}',
        'booking_date': DateFormat('dd MMM yyyy, hh:mm a').format(DateTime.now()),
        'subject': 'Booking Confirmation - $hotelName',
        'message': _generateBookingConfirmationMessage(
          customerName: customerName,
          bookingId: bookingId,
          hotelName: hotelName,
          roomName: roomName,
          checkInDate: checkInDate,
          checkOutDate: checkOutDate,
          amount: amount,
        ),
      };

      // Simulate email sending
      await Future.delayed(const Duration(seconds: 1));

      // Simulate successful email sending (90% success rate)
      bool emailSent = DateTime.now().millisecond % 10 != 0;

      if (emailSent) {
        print('Booking confirmation email sent successfully (Demo Mode)');
        print('Email sent to: $toEmail');
        print('Subject: Booking Confirmation - $hotelName');
        return true;
      } else {
        print('Failed to send booking confirmation email (Demo Mode)');
        return false;
      }
    } catch (e) {
      print('Error sending booking confirmation email: $e');
      return false;
    }
  }

  // Send payment confirmation email
  Future<bool> sendPaymentConfirmation({
    required String toEmail,
    required String customerName,
    required String bookingId,
    required String hotelName,
    required String roomName,
    required DateTime checkInDate,
    required DateTime checkOutDate,
    required double amount,
    required String orderId,
  }) async {
    try {
      await initializeEmailJS();

      Map<String, dynamic> templateParams = {
        'to_email': toEmail,
        'customer_name': customerName,
        'booking_id': bookingId,
        'order_id': orderId,
        'hotel_name': hotelName,
        'room_name': roomName,
        'check_in_date': DateFormat('dd MMM yyyy').format(checkInDate),
        'check_out_date': DateFormat('dd MMM yyyy').format(checkOutDate),
        'amount': '₹${amount.toStringAsFixed(2)}',
        'payment_date': DateFormat('dd MMM yyyy, hh:mm a').format(DateTime.now()),
        'subject': 'Payment Successful - $hotelName Booking',
        'message': _generatePaymentConfirmationMessage(
          customerName: customerName,
          bookingId: bookingId,
          orderId: orderId,
          hotelName: hotelName,
          roomName: roomName,
          checkInDate: checkInDate,
          checkOutDate: checkOutDate,
          amount: amount,
        ),
      };

      // Simulate email sending
      await Future.delayed(const Duration(seconds: 1));

      // Simulate successful email sending (90% success rate)
      bool emailSent = DateTime.now().millisecond % 10 != 0;

      if (emailSent) {
        print('Payment confirmation email sent successfully (Demo Mode)');
        print('Email sent to: $toEmail');
        print('Subject: Payment Successful - $hotelName Booking');
        return true;
      } else {
        print('Failed to send payment confirmation email (Demo Mode)');
        return false;
      }
    } catch (e) {
      print('Error sending payment confirmation email: $e');
      return false;
    }
  }

  // Send payment failure notification
  Future<bool> sendPaymentFailureNotification({
    required String toEmail,
    required String customerName,
    required String bookingId,
    required String failureReason,
  }) async {
    try {
      await initializeEmailJS();

      Map<String, dynamic> templateParams = {
        'to_email': toEmail,
        'customer_name': customerName,
        'booking_id': bookingId,
        'failure_reason': failureReason,
        'support_email': '<EMAIL>',
        'support_phone': '+91-**********',
        'subject': 'Payment Failed - Booking $bookingId',
        'message': _generatePaymentFailureMessage(
          customerName: customerName,
          bookingId: bookingId,
          failureReason: failureReason,
        ),
      };

      // Simulate email sending
      await Future.delayed(const Duration(seconds: 1));

      // Simulate successful email sending (90% success rate)
      bool emailSent = DateTime.now().millisecond % 10 != 0;

      if (emailSent) {
        print('Payment failure notification sent successfully (Demo Mode)');
        print('Email sent to: $toEmail');
        print('Subject: Payment Failed - Booking $bookingId');
        return true;
      } else {
        print('Failed to send payment failure notification (Demo Mode)');
        return false;
      }
    } catch (e) {
      print('Error sending payment failure notification: $e');
      return false;
    }
  }

  // Generate booking confirmation message
  String _generateBookingConfirmationMessage({
    required String customerName,
    required String bookingId,
    required String hotelName,
    required String roomName,
    required DateTime checkInDate,
    required DateTime checkOutDate,
    required double amount,
  }) {
    return '''
Dear $customerName,

Thank you for choosing our hotel booking service! Your booking has been confirmed.

Booking Details:
- Booking ID: $bookingId
- Hotel: $hotelName
- Room: $roomName
- Check-in: ${DateFormat('dd MMM yyyy').format(checkInDate)}
- Check-out: ${DateFormat('dd MMM yyyy').format(checkOutDate)}
- Total Amount: ₹${amount.toStringAsFixed(2)}

Please save this email for your records. You can use your Booking ID to manage your reservation.

Important Information:
- Check-in time: 2:00 PM
- Check-out time: 12:00 PM
- Please carry a valid ID proof during check-in
- For any changes or cancellations, please contact us at least 24 hours before check-in

We look forward to hosting you!

Best regards,
Hotel Booking Team
''';
  }

  // Generate payment confirmation message
  String _generatePaymentConfirmationMessage({
    required String customerName,
    required String bookingId,
    required String orderId,
    required String hotelName,
    required String roomName,
    required DateTime checkInDate,
    required DateTime checkOutDate,
    required double amount,
  }) {
    return '''
Dear $customerName,

Your payment has been processed successfully! Your hotel booking is now confirmed.

Payment Details:
- Order ID: $orderId
- Booking ID: $bookingId
- Amount Paid: ₹${amount.toStringAsFixed(2)}
- Payment Date: ${DateFormat('dd MMM yyyy, hh:mm a').format(DateTime.now())}

Booking Details:
- Hotel: $hotelName
- Room: $roomName
- Check-in: ${DateFormat('dd MMM yyyy').format(checkInDate)}
- Check-out: ${DateFormat('dd MMM yyyy').format(checkOutDate)}

Your booking is confirmed and you will receive a separate booking confirmation email shortly.

Thank you for your payment!

Best regards,
Hotel Booking Team
''';
  }

  // Generate payment failure message
  String _generatePaymentFailureMessage({
    required String customerName,
    required String bookingId,
    required String failureReason,
  }) {
    return '''
Dear $customerName,

We regret to inform you that your payment for booking $bookingId could not be processed.

Reason: $failureReason

What you can do:
1. Try making the payment again with a different payment method
2. Check if your card has sufficient balance
3. Contact your bank if the issue persists
4. Reach out to our support team for assistance

Your booking is still reserved for the next 30 minutes. Please complete the payment to confirm your reservation.

For assistance, please contact:
- Email: <EMAIL>
- Phone: +91-**********

We apologize for the inconvenience.

Best regards,
Hotel Booking Team
''';
  }

  // Send booking cancellation email
  Future<bool> sendBookingCancellation({
    required String toEmail,
    required String customerName,
    required String bookingId,
    required String hotelName,
  }) async {
    try {
      await initializeEmailJS();

      Map<String, dynamic> templateParams = {
        'to_email': toEmail,
        'customer_name': customerName,
        'booking_id': bookingId,
        'hotel_name': hotelName,
        'cancellation_date': DateFormat('dd MMM yyyy, hh:mm a').format(DateTime.now()),
        'subject': 'Booking Cancelled - $bookingId',
        'message': '''
Dear $customerName,

Your booking $bookingId for $hotelName has been successfully cancelled.

If you have made any payment, the refund will be processed within 5-7 business days.

Thank you for using our service.

Best regards,
Hotel Booking Team
''',
      };

      // Simulate email sending
      await Future.delayed(const Duration(seconds: 1));

      // Simulate successful email sending (90% success rate)
      bool emailSent = DateTime.now().millisecond % 10 != 0;

      if (emailSent) {
        print('Booking cancellation email sent successfully (Demo Mode)');
        print('Email sent to: $toEmail');
        print('Subject: Booking Cancelled - $bookingId');
      }

      return emailSent;
    } catch (e) {
      print('Error sending booking cancellation email: $e');
      return false;
    }
  }
}
