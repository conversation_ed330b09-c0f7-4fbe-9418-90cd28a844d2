import 'dart:math' as math;
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/hotel.dart';
import '../models/booking.dart';
import '../models/review.dart';

class HotelService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Get all hotels
  Stream<List<Hotel>> getHotels() {
    return _firestore
        .collection('hotels')
        .where('isAvailable', isEqualTo: true)
        .orderBy('rating', descending: true)
        .snapshots()
        .map((snapshot) => snapshot.docs.map((doc) => Hotel.fromFirestore(doc)).toList());
  }

  // Get hotels by city
  Stream<List<Hotel>> getHotelsByCity(String city) {
    return _firestore
        .collection('hotels')
        .where('city', isEqualTo: city)
        .where('isAvailable', isEqualTo: true)
        .orderBy('rating', descending: true)
        .snapshots()
        .map((snapshot) => snapshot.docs.map((doc) => Hotel.fromFirestore(doc)).toList());
  }

  // Search hotels
  Future<List<Hotel>> searchHotels({
    String? query,
    String? city,
    double? minRating,
    double? maxPrice,
    List<String>? amenities,
  }) async {
    Query hotelQuery = _firestore.collection('hotels').where('isAvailable', isEqualTo: true);

    if (city != null && city.isNotEmpty) {
      hotelQuery = hotelQuery.where('city', isEqualTo: city);
    }

    if (minRating != null) {
      hotelQuery = hotelQuery.where('rating', isGreaterThanOrEqualTo: minRating);
    }

    if (maxPrice != null) {
      hotelQuery = hotelQuery.where('pricePerNight', isLessThanOrEqualTo: maxPrice);
    }

    QuerySnapshot snapshot = await hotelQuery.get();
    List<Hotel> hotels = snapshot.docs.map((doc) => Hotel.fromFirestore(doc)).toList();

    // Filter by query (name or description)
    if (query != null && query.isNotEmpty) {
      hotels = hotels.where((hotel) =>
          hotel.name.toLowerCase().contains(query.toLowerCase()) ||
          hotel.description.toLowerCase().contains(query.toLowerCase())).toList();
    }

    // Filter by amenities
    if (amenities != null && amenities.isNotEmpty) {
      hotels = hotels.where((hotel) =>
          amenities.every((amenity) => hotel.amenities.contains(amenity))).toList();
    }

    return hotels;
  }

  // Get hotel by ID
  Future<Hotel?> getHotelById(String hotelId) async {
    try {
      DocumentSnapshot doc = await _firestore.collection('hotels').doc(hotelId).get();
      if (doc.exists) {
        return Hotel.fromFirestore(doc);
      }
      return null;
    } catch (e) {
      throw 'Error fetching hotel: $e';
    }
  }

  // Get rooms for a hotel
  Stream<List<Room>> getRoomsForHotel(String hotelId) {
    return _firestore
        .collection('rooms')
        .where('hotelId', isEqualTo: hotelId)
        .where('isAvailable', isEqualTo: true)
        .orderBy('pricePerNight')
        .snapshots()
        .map((snapshot) => snapshot.docs.map((doc) => Room.fromFirestore(doc)).toList());
  }

  // Get room by ID
  Future<Room?> getRoomById(String roomId) async {
    try {
      DocumentSnapshot doc = await _firestore.collection('rooms').doc(roomId).get();
      if (doc.exists) {
        return Room.fromFirestore(doc);
      }
      return null;
    } catch (e) {
      throw 'Error fetching room: $e';
    }
  }

  // Check room availability
  Future<bool> checkRoomAvailability({
    required String roomId,
    required DateTime checkIn,
    required DateTime checkOut,
  }) async {
    try {
      // Check if there are any conflicting bookings
      QuerySnapshot bookings = await _firestore
          .collection('bookings')
          .where('roomId', isEqualTo: roomId)
          .where('status', whereIn: ['confirmed', 'checkedIn'])
          .get();

      for (var booking in bookings.docs) {
        Booking existingBooking = Booking.fromFirestore(booking);
        
        // Check for date overlap
        if (checkIn.isBefore(existingBooking.checkOutDate) &&
            checkOut.isAfter(existingBooking.checkInDate)) {
          return false; // Room is not available
        }
      }

      return true; // Room is available
    } catch (e) {
      throw 'Error checking room availability: $e';
    }
  }

  // Get featured hotels
  Future<List<Hotel>> getFeaturedHotels({int limit = 10}) async {
    try {
      QuerySnapshot snapshot = await _firestore
          .collection('hotels')
          .where('isAvailable', isEqualTo: true)
          .orderBy('rating', descending: true)
          .limit(limit)
          .get();

      return snapshot.docs.map((doc) => Hotel.fromFirestore(doc)).toList();
    } catch (e) {
      throw 'Error fetching featured hotels: $e';
    }
  }

  // Get recommended hotels based on user preferences
  Future<List<Hotel>> getRecommendedHotels({
    String? userId,
    String? preferredCity,
    double? maxBudget,
    int limit = 10,
  }) async {
    try {
      Query query = _firestore
          .collection('hotels')
          .where('isAvailable', isEqualTo: true);

      if (preferredCity != null) {
        query = query.where('city', isEqualTo: preferredCity);
      }

      if (maxBudget != null) {
        query = query.where('pricePerNight', isLessThanOrEqualTo: maxBudget);
      }

      QuerySnapshot snapshot = await query
          .orderBy('rating', descending: true)
          .limit(limit)
          .get();

      return snapshot.docs.map((doc) => Hotel.fromFirestore(doc)).toList();
    } catch (e) {
      throw 'Error fetching recommended hotels: $e';
    }
  }

  // Get nearby hotels (requires location)
  Future<List<Hotel>> getNearbyHotels({
    required double latitude,
    required double longitude,
    double radiusInKm = 10.0,
    int limit = 20,
  }) async {
    try {
      // Note: This is a simplified implementation
      // For production, consider using GeoFlutterFire or similar for proper geospatial queries
      QuerySnapshot snapshot = await _firestore
          .collection('hotels')
          .where('isAvailable', isEqualTo: true)
          .limit(limit * 2) // Get more to filter by distance
          .get();

      List<Hotel> hotels = snapshot.docs.map((doc) => Hotel.fromFirestore(doc)).toList();

      // Filter by distance (simplified calculation)
      hotels = hotels.where((hotel) {
        double distance = _calculateDistance(
          latitude, longitude,
          hotel.latitude, hotel.longitude,
        );
        return distance <= radiusInKm;
      }).toList();

      // Sort by distance
      hotels.sort((a, b) {
        double distanceA = _calculateDistance(latitude, longitude, a.latitude, a.longitude);
        double distanceB = _calculateDistance(latitude, longitude, b.latitude, b.longitude);
        return distanceA.compareTo(distanceB);
      });

      return hotels.take(limit).toList();
    } catch (e) {
      throw 'Error fetching nearby hotels: $e';
    }
  }

  // Calculate distance between two points (Haversine formula)
  double _calculateDistance(double lat1, double lon1, double lat2, double lon2) {
    const double earthRadius = 6371; // Earth's radius in kilometers
    
    double dLat = _degreesToRadians(lat2 - lat1);
    double dLon = _degreesToRadians(lon2 - lon1);
    
    double a = math.sin(dLat / 2) * math.sin(dLat / 2) +
        math.cos(_degreesToRadians(lat1)) * math.cos(_degreesToRadians(lat2)) *
        math.sin(dLon / 2) * math.sin(dLon / 2);
    
    double c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a));
    
    return earthRadius * c;
  }

  double _degreesToRadians(double degrees) {
    return degrees * (math.pi / 180);
  }
}
