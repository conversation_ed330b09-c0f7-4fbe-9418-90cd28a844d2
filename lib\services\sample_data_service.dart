import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/hotel.dart';
import '../models/room.dart';

class SampleDataService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Sample hotel data - Indian Hotels with INR pricing
  static List<Map<String, dynamic>> sampleHotels = [
    {
      'name': 'The Taj Mahal Palace',
      'description': 'Iconic luxury hotel in Mumbai with heritage architecture and world-class amenities overlooking the Gateway of India.',
      'address': 'Apollo Bunder, Colaba, Mumbai',
      'city': 'Mumbai',
      'country': 'India',
      'latitude': 18.9220,
      'longitude': 72.8347,
      'images': [
        'https://images.unsplash.com/photo-1566073771259-6a8506099945?w=800&q=80',
        'https://images.unsplash.com/photo-1571003123894-1f0594d2b5d9?w=800&q=80',
        'https://images.unsplash.com/photo-1582719478250-c89cae4dc85b?w=800&q=80',
        'https://images.unsplash.com/photo-1520250497591-112f2f40a3f4?w=800&q=80',
      ],
      'rating': 4.8,
      'reviewCount': 1245,
      'amenities': ['WiFi', 'Pool', 'Gym', 'Spa', 'Restaurant', 'Bar', 'Room Service', 'Parking', 'Business Center'],
      'pricePerNight': 15000.0,
      'currency': 'INR',
      'isAvailable': true,
    },
    {
      'name': 'ITC Grand Chola',
      'description': 'Magnificent luxury hotel in Chennai inspired by Chola architecture with premium amenities and services.',
      'address': '63 Mount Road, Guindy, Chennai',
      'city': 'Chennai',
      'country': 'India',
      'latitude': 13.0067,
      'longitude': 80.2206,
      'images': [
        'https://images.unsplash.com/photo-1520250497591-112f2f40a3f4?w=800',
        'https://images.unsplash.com/photo-1571896349842-33c89424de2d?w=800',
        'https://images.unsplash.com/photo-1578683010236-d716f9a3f461?w=800',
      ],
      'rating': 4.6,
      'reviewCount': 892,
      'amenities': ['WiFi', 'Pool', 'Restaurant', 'Bar', 'Airport Shuttle', 'Laundry', 'Spa', 'Business Center'],
      'pricePerNight': 12000.0,
      'currency': 'INR',
      'isAvailable': true,
    },
    {
      'name': 'Leela Palace Goa',
      'description': 'Beachfront luxury resort in Goa with pristine beaches, world-class spa, and authentic Goan hospitality.',
      'address': 'Mobor Beach, Cavelossim, Goa',
      'city': 'Goa',
      'country': 'India',
      'latitude': 15.2993,
      'longitude': 74.1240,
      'images': [
        'https://images.unsplash.com/photo-1571896349842-33c89424de2d?w=800',
        'https://images.unsplash.com/photo-1582719478250-c89cae4dc85b?w=800',
        'https://images.unsplash.com/photo-1566073771259-6a8506099945?w=800',
      ],
      'rating': 4.7,
      'reviewCount': 756,
      'amenities': ['WiFi', 'Pool', 'Beach Access', 'Restaurant', 'Bar', 'Water Sports', 'Spa', 'Golf Course'],
      'pricePerNight': 18000.0,
      'currency': 'INR',
      'isAvailable': true,
    },
    {
      'name': 'Wildflower Hall Shimla',
      'description': 'Luxury mountain resort in Shimla with breathtaking Himalayan views and world-class amenities.',
      'address': 'Chharabra, Shimla, Himachal Pradesh',
      'city': 'Shimla',
      'country': 'India',
      'latitude': 31.1048,
      'longitude': 77.1734,
      'images': [
        'https://images.unsplash.com/photo-1578683010236-d716f9a3f461?w=800',
        'https://images.unsplash.com/photo-1520250497591-112f2f40a3f4?w=800',
        'https://images.unsplash.com/photo-1571003123894-1f0594d2b5d9?w=800',
      ],
      'rating': 4.5,
      'reviewCount': 456,
      'amenities': ['WiFi', 'Restaurant', 'Hiking Trails', 'Nature Tours', 'Parking', 'Spa', 'Adventure Sports'],
      'pricePerNight': 8500.0,
      'currency': 'INR',
      'isAvailable': true,
    },
    {
      'name': 'Rambagh Palace Jaipur',
      'description': 'Former royal palace turned luxury hotel in the Pink City with regal architecture and royal hospitality.',
      'address': 'Bhawani Singh Road, Jaipur, Rajasthan',
      'city': 'Jaipur',
      'country': 'India',
      'latitude': 26.9124,
      'longitude': 75.7873,
      'images': [
        'https://images.unsplash.com/photo-1571896349842-33c89424de2d?w=800',
        'https://images.unsplash.com/photo-1566073771259-6a8506099945?w=800',
        'https://images.unsplash.com/photo-1582719478250-c89cae4dc85b?w=800',
      ],
      'rating': 4.6,
      'reviewCount': 634,
      'amenities': ['WiFi', 'Restaurant', 'Palace Tours', 'Cultural Shows', 'Royal Dining', 'Spa', 'Heritage Walks'],
      'pricePerNight': 14000.0,
      'currency': 'INR',
      'isAvailable': true,
    },
    {
      'name': 'Ramana Maharshi Ashram Resort',
      'description': 'Spiritual retreat resort in Tiruvannamalai with serene ambiance, mountain views, and proximity to Arunachala Temple.',
      'address': 'Ramana Nagar, Tiruvannamalai, Tamil Nadu',
      'city': 'Tiruvannamalai',
      'country': 'India',
      'latitude': 12.2253,
      'longitude': 79.0747,
      'images': [
        'https://images.unsplash.com/photo-1578683010236-d716f9a3f461?w=800&q=80',
        'https://images.unsplash.com/photo-1520250497591-112f2f40a3f4?w=800&q=80',
        'https://images.unsplash.com/photo-1571003123894-1f0594d2b5d9?w=800&q=80',
        'https://images.unsplash.com/photo-1590490360182-c33d57733427?w=800&q=80',
      ],
      'rating': 4.4,
      'reviewCount': 289,
      'amenities': ['WiFi', 'Restaurant', 'Temple Tours', 'Meditation Hall', 'Garden', 'Parking', 'Spiritual Library'],
      'pricePerNight': 6500.0,
      'currency': 'INR',
      'isAvailable': true,
    },
  ];

  // Sample room data for each hotel - Indian Hotels with INR pricing
  static Map<String, List<Map<String, dynamic>>> sampleRooms = {
    'The Taj Mahal Palace': [
      {
        'name': 'Deluxe King Room',
        'description': 'Spacious room with king bed, harbor view, and luxury amenities.',
        'type': 'Deluxe',
        'images': [
          'https://images.unsplash.com/photo-1631049307264-da0ec9d70304?w=800&q=80',
          'https://images.unsplash.com/photo-1618773928121-c32242e63f39?w=800&q=80',
          'https://images.unsplash.com/photo-1590490360182-c33d57733427?w=800&q=80',
        ],
        'pricePerNight': 15000.0,
        'currency': 'INR',
        'maxOccupancy': 2,
        'amenities': ['WiFi', 'Air Conditioning', 'Mini Bar', 'Safe', 'Balcony', '24/7 Room Service'],
        'isAvailable': true,
        'availableRooms': 5,
        'size': 45.0,
        'bedType': 'King Bed',
        'hasBalcony': true,
        'hasKitchen': false,
        'view': 'Harbor View',
      },
      {
        'name': 'Presidential Suite',
        'description': 'Luxurious presidential suite with separate living area, dining room, and premium amenities.',
        'type': 'Presidential Suite',
        'images': [
          'https://images.unsplash.com/photo-1618773928121-c32242e63f39?w=800',
          'https://images.unsplash.com/photo-1631049307264-da0ec9d70304?w=800',
        ],
        'pricePerNight': 35000.0,
        'currency': 'INR',
        'maxOccupancy': 4,
        'amenities': ['WiFi', 'Air Conditioning', 'Mini Bar', 'Safe', 'Living Area', 'Kitchenette', 'Butler Service'],
        'isAvailable': true,
        'availableRooms': 2,
        'size': 85.0,
        'bedType': 'King Bed + Sofa Bed',
        'hasBalcony': true,
        'hasKitchen': true,
        'view': 'Gateway of India View',
      },
    ],
    'Angkor Heritage Hotel': [
      {
        'name': 'Traditional Twin Room',
        'description': 'Comfortable room with twin beds and traditional Khmer decor.',
        'type': 'Standard',
        'images': [
          'https://images.unsplash.com/photo-1631049307264-da0ec9d70304?w=800',
        ],
        'pricePerNight': 120.0,
        'currency': 'USD',
        'maxOccupancy': 2,
        'amenities': ['WiFi', 'Air Conditioning', 'Safe'],
        'isAvailable': true,
        'availableRooms': 8,
        'size': 28.0,
        'bedType': 'Twin Beds',
        'hasBalcony': false,
        'hasKitchen': false,
        'view': 'Garden View',
      },
    ],
    'Seaside Resort': [
      {
        'name': 'Ocean View Villa',
        'description': 'Private villa with direct beach access and stunning ocean views.',
        'type': 'Villa',
        'images': [
          'https://images.unsplash.com/photo-1618773928121-c32242e63f39?w=800',
        ],
        'pricePerNight': 300.0,
        'currency': 'USD',
        'maxOccupancy': 6,
        'amenities': ['WiFi', 'Air Conditioning', 'Kitchen', 'Private Beach', 'Terrace'],
        'isAvailable': true,
        'availableRooms': 3,
        'size': 80.0,
        'bedType': '2 King Beds',
        'hasBalcony': true,
        'hasKitchen': true,
        'view': 'Ocean View',
      },
    ],
    'Ramana Maharshi Ashram Resort': [
      {
        'name': 'Spiritual Retreat Room',
        'description': 'Peaceful room with mountain view, meditation corner, and spiritual ambiance.',
        'type': 'Spiritual',
        'images': [
          'https://images.unsplash.com/photo-1631049307264-da0ec9d70304?w=800&q=80',
          'https://images.unsplash.com/photo-1618773928121-c32242e63f39?w=800&q=80',
          'https://images.unsplash.com/photo-1590490360182-c33d57733427?w=800&q=80',
        ],
        'pricePerNight': 6500.0,
        'currency': 'INR',
        'maxOccupancy': 2,
        'amenities': ['WiFi', 'Air Conditioning', 'Meditation Corner', 'Mountain View', 'Spiritual Books'],
        'isAvailable': true,
        'availableRooms': 8,
        'size': 30.0,
        'bedType': 'Twin Beds',
        'hasBalcony': true,
        'hasKitchen': false,
        'view': 'Arunachala Mountain View',
      },
      {
        'name': 'Ashram Deluxe Suite',
        'description': 'Spacious suite with separate meditation area, temple view, and premium spiritual amenities.',
        'type': 'Deluxe Suite',
        'images': [
          'https://images.unsplash.com/photo-1618773928121-c32242e63f39?w=800&q=80',
          'https://images.unsplash.com/photo-1631049307264-da0ec9d70304?w=800&q=80',
          'https://images.unsplash.com/photo-1578683010236-d716f9a3f461?w=800&q=80',
        ],
        'pricePerNight': 9500.0,
        'currency': 'INR',
        'maxOccupancy': 3,
        'amenities': ['WiFi', 'Air Conditioning', 'Meditation Area', 'Temple View', 'Spiritual Library', 'Balcony'],
        'isAvailable': true,
        'availableRooms': 4,
        'size': 45.0,
        'bedType': 'King Bed + Single Bed',
        'hasBalcony': true,
        'hasKitchen': false,
        'view': 'Arunachaleswarar Temple View',
      },
      {
        'name': 'Family Spiritual Cottage',
        'description': 'Traditional cottage-style accommodation perfect for families seeking spiritual retreat.',
        'type': 'Family Cottage',
        'images': [
          'https://images.unsplash.com/photo-1578683010236-d716f9a3f461?w=800&q=80',
          'https://images.unsplash.com/photo-1520250497591-112f2f40a3f4?w=800&q=80',
          'https://images.unsplash.com/photo-1571003123894-1f0594d2b5d9?w=800&q=80',
        ],
        'pricePerNight': 8000.0,
        'currency': 'INR',
        'maxOccupancy': 4,
        'amenities': ['WiFi', 'Air Conditioning', 'Kitchenette', 'Garden View', 'Family Meditation Space', 'Parking'],
        'isAvailable': true,
        'availableRooms': 6,
        'size': 55.0,
        'bedType': '2 Double Beds',
        'hasBalcony': false,
        'hasKitchen': true,
        'view': 'Garden & Mountain View',
      },
    ],
  };

  // Add sample data to Firestore
  Future<void> addSampleData() async {
    try {
      // Add hotels
      for (var hotelData in sampleHotels) {
        DocumentReference hotelRef = await _firestore.collection('hotels').add({
          ...hotelData,
          'createdAt': FieldValue.serverTimestamp(),
          'updatedAt': FieldValue.serverTimestamp(),
        });

        // Add rooms for this hotel
        String hotelName = hotelData['name'];
        if (sampleRooms.containsKey(hotelName)) {
          for (var roomData in sampleRooms[hotelName]!) {
            await _firestore.collection('rooms').add({
              ...roomData,
              'hotelId': hotelRef.id,
            });
          }
        }
      }

      print('Sample data added successfully!');
    } catch (e) {
      print('Error adding sample data: $e');
      throw e;
    }
  }

  // Check if sample data exists
  Future<bool> hasSampleData() async {
    try {
      QuerySnapshot snapshot = await _firestore.collection('hotels').limit(1).get();
      return snapshot.docs.isNotEmpty;
    } catch (e) {
      print('Error checking sample data: $e');
      return false;
    }
  }

  // Initialize sample data if needed
  Future<void> initializeSampleDataIfNeeded() async {
    try {
      bool hasData = await hasSampleData();
      if (!hasData) {
        await addSampleData();
      }
    } catch (e) {
      print('Error initializing sample data: $e');
    }
  }
}
