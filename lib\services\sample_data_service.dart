import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/hotel.dart';
import '../models/room.dart';

class SampleDataService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Sample hotel data
  static List<Map<String, dynamic>> sampleHotels = [
    {
      'name': 'Grand Palace Hotel',
      'description': 'Luxury hotel in the heart of Phnom Penh with stunning city views and world-class amenities.',
      'address': '123 Sisowath Quay, Phnom Penh',
      'city': 'Phnom Penh',
      'country': 'Cambodia',
      'latitude': 11.5564,
      'longitude': 104.9282,
      'images': [
        'https://images.unsplash.com/photo-1566073771259-6a8506099945?w=800',
        'https://images.unsplash.com/photo-1571003123894-1f0594d2b5d9?w=800',
        'https://images.unsplash.com/photo-1582719478250-c89cae4dc85b?w=800',
      ],
      'rating': 4.8,
      'reviewCount': 245,
      'amenities': ['WiFi', 'Pool', 'Gym', 'Spa', 'Restaurant', 'Bar', 'Room Service', 'Parking'],
      'pricePerNight': 150.0,
      'currency': 'USD',
      'isAvailable': true,
    },
    {
      'name': 'Angkor Heritage Hotel',
      'description': 'Traditional Khmer architecture meets modern comfort in this beautiful Siem Reap hotel.',
      'address': '456 Pub Street, Siem Reap',
      'city': 'Siem Reap',
      'country': 'Cambodia',
      'latitude': 13.3671,
      'longitude': 103.8448,
      'images': [
        'https://images.unsplash.com/photo-1520250497591-112f2f40a3f4?w=800',
        'https://images.unsplash.com/photo-1571896349842-33c89424de2d?w=800',
        'https://images.unsplash.com/photo-1578683010236-d716f9a3f461?w=800',
      ],
      'rating': 4.6,
      'reviewCount': 189,
      'amenities': ['WiFi', 'Pool', 'Restaurant', 'Bar', 'Airport Shuttle', 'Laundry'],
      'pricePerNight': 120.0,
      'currency': 'USD',
      'isAvailable': true,
    },
    {
      'name': 'Seaside Resort',
      'description': 'Beachfront resort with pristine white sand beaches and crystal clear waters.',
      'address': '789 Beach Road, Sihanoukville',
      'city': 'Sihanoukville',
      'country': 'Cambodia',
      'latitude': 10.6104,
      'longitude': 103.5390,
      'images': [
        'https://images.unsplash.com/photo-1571896349842-33c89424de2d?w=800',
        'https://images.unsplash.com/photo-1582719478250-c89cae4dc85b?w=800',
        'https://images.unsplash.com/photo-1566073771259-6a8506099945?w=800',
      ],
      'rating': 4.4,
      'reviewCount': 156,
      'amenities': ['WiFi', 'Pool', 'Beach Access', 'Restaurant', 'Bar', 'Water Sports'],
      'pricePerNight': 200.0,
      'currency': 'USD',
      'isAvailable': true,
    },
    {
      'name': 'Mountain View Lodge',
      'description': 'Eco-friendly lodge nestled in the mountains with breathtaking views and nature trails.',
      'address': '321 Mountain Road, Mondulkiri',
      'city': 'Mondulkiri',
      'country': 'Cambodia',
      'latitude': 12.4545,
      'longitude': 107.2067,
      'images': [
        'https://images.unsplash.com/photo-1578683010236-d716f9a3f461?w=800',
        'https://images.unsplash.com/photo-1520250497591-112f2f40a3f4?w=800',
        'https://images.unsplash.com/photo-1571003123894-1f0594d2b5d9?w=800',
      ],
      'rating': 4.2,
      'reviewCount': 98,
      'amenities': ['WiFi', 'Restaurant', 'Hiking Trails', 'Nature Tours', 'Parking'],
      'pricePerNight': 80.0,
      'currency': 'USD',
      'isAvailable': true,
    },
    {
      'name': 'Riverside Boutique Hotel',
      'description': 'Charming boutique hotel along the Mekong River with traditional Cambodian hospitality.',
      'address': '654 Riverside Drive, Kampot',
      'city': 'Kampot',
      'country': 'Cambodia',
      'latitude': 10.6104,
      'longitude': 104.1776,
      'images': [
        'https://images.unsplash.com/photo-1571896349842-33c89424de2d?w=800',
        'https://images.unsplash.com/photo-1566073771259-6a8506099945?w=800',
        'https://images.unsplash.com/photo-1582719478250-c89cae4dc85b?w=800',
      ],
      'rating': 4.5,
      'reviewCount': 134,
      'amenities': ['WiFi', 'Restaurant', 'River View', 'Boat Tours', 'Bicycle Rental'],
      'pricePerNight': 90.0,
      'currency': 'USD',
      'isAvailable': true,
    },
  ];

  // Sample room data for each hotel
  static Map<String, List<Map<String, dynamic>>> sampleRooms = {
    'Grand Palace Hotel': [
      {
        'name': 'Deluxe King Room',
        'description': 'Spacious room with king bed, city view, and modern amenities.',
        'type': 'Deluxe',
        'images': [
          'https://images.unsplash.com/photo-1631049307264-da0ec9d70304?w=800',
          'https://images.unsplash.com/photo-1618773928121-c32242e63f39?w=800',
        ],
        'pricePerNight': 150.0,
        'currency': 'USD',
        'maxOccupancy': 2,
        'amenities': ['WiFi', 'Air Conditioning', 'Mini Bar', 'Safe', 'Balcony'],
        'isAvailable': true,
        'availableRooms': 5,
        'size': 35.0,
        'bedType': 'King Bed',
        'hasBalcony': true,
        'hasKitchen': false,
        'view': 'City View',
      },
      {
        'name': 'Executive Suite',
        'description': 'Luxurious suite with separate living area and premium amenities.',
        'type': 'Suite',
        'images': [
          'https://images.unsplash.com/photo-1618773928121-c32242e63f39?w=800',
          'https://images.unsplash.com/photo-1631049307264-da0ec9d70304?w=800',
        ],
        'pricePerNight': 250.0,
        'currency': 'USD',
        'maxOccupancy': 4,
        'amenities': ['WiFi', 'Air Conditioning', 'Mini Bar', 'Safe', 'Living Area', 'Kitchenette'],
        'isAvailable': true,
        'availableRooms': 2,
        'size': 65.0,
        'bedType': 'King Bed + Sofa Bed',
        'hasBalcony': true,
        'hasKitchen': true,
        'view': 'River View',
      },
    ],
    'Angkor Heritage Hotel': [
      {
        'name': 'Traditional Twin Room',
        'description': 'Comfortable room with twin beds and traditional Khmer decor.',
        'type': 'Standard',
        'images': [
          'https://images.unsplash.com/photo-1631049307264-da0ec9d70304?w=800',
        ],
        'pricePerNight': 120.0,
        'currency': 'USD',
        'maxOccupancy': 2,
        'amenities': ['WiFi', 'Air Conditioning', 'Safe'],
        'isAvailable': true,
        'availableRooms': 8,
        'size': 28.0,
        'bedType': 'Twin Beds',
        'hasBalcony': false,
        'hasKitchen': false,
        'view': 'Garden View',
      },
    ],
    'Seaside Resort': [
      {
        'name': 'Ocean View Villa',
        'description': 'Private villa with direct beach access and stunning ocean views.',
        'type': 'Villa',
        'images': [
          'https://images.unsplash.com/photo-1618773928121-c32242e63f39?w=800',
        ],
        'pricePerNight': 300.0,
        'currency': 'USD',
        'maxOccupancy': 6,
        'amenities': ['WiFi', 'Air Conditioning', 'Kitchen', 'Private Beach', 'Terrace'],
        'isAvailable': true,
        'availableRooms': 3,
        'size': 80.0,
        'bedType': '2 King Beds',
        'hasBalcony': true,
        'hasKitchen': true,
        'view': 'Ocean View',
      },
    ],
  };

  // Add sample data to Firestore
  Future<void> addSampleData() async {
    try {
      // Add hotels
      for (var hotelData in sampleHotels) {
        DocumentReference hotelRef = await _firestore.collection('hotels').add({
          ...hotelData,
          'createdAt': FieldValue.serverTimestamp(),
          'updatedAt': FieldValue.serverTimestamp(),
        });

        // Add rooms for this hotel
        String hotelName = hotelData['name'];
        if (sampleRooms.containsKey(hotelName)) {
          for (var roomData in sampleRooms[hotelName]!) {
            await _firestore.collection('rooms').add({
              ...roomData,
              'hotelId': hotelRef.id,
            });
          }
        }
      }

      print('Sample data added successfully!');
    } catch (e) {
      print('Error adding sample data: $e');
      throw e;
    }
  }

  // Check if sample data exists
  Future<bool> hasSampleData() async {
    try {
      QuerySnapshot snapshot = await _firestore.collection('hotels').limit(1).get();
      return snapshot.docs.isNotEmpty;
    } catch (e) {
      print('Error checking sample data: $e');
      return false;
    }
  }

  // Initialize sample data if needed
  Future<void> initializeSampleDataIfNeeded() async {
    try {
      bool hasData = await hasSampleData();
      if (!hasData) {
        await addSampleData();
      }
    } catch (e) {
      print('Error initializing sample data: $e');
    }
  }
}
