name: hotel_booking
description: One Touch - Hotel Booking App

publish_to: 'none' # Remove this line if you wish to publish to pub.dev

version: 1.0.0+1

environment:
  sdk: ">=3.0.0 <4.0.0"

dependencies:
  flutter:
    sdk: flutter

  cupertino_icons: ^1.0.2
  flutter_svg: ^2.0.10+1
  cached_network_image: ^3.1.0
  badges: ^3.1.2
  carousel_slider: ^5.1.1
  image_stack: ^2.1.1
  firebase_core: ^3.15.1
  firebase_auth: ^5.6.2
  cloud_firestore: ^5.6.11
  path_provider: ^2.1.5
  firebase_crashlytics: ^4.3.9
  firebase_analytics: ^11.5.2
  flutter_launcher_icons: ^0.13.1

  # State Management
  provider: ^6.1.2

  # HTTP and API
  http: ^1.2.2
  dio: ^5.7.0

  # Date and Time
  intl: ^0.19.0

  # Local Storage
  shared_preferences: ^2.3.2
  sqflite: ^2.4.1

  # UI Enhancements
  shimmer: ^3.0.0
  lottie: ^3.1.2
  flutter_rating_bar: ^4.0.1
  smooth_page_indicator: ^1.2.0

  # Maps and Location
  geolocator: ^13.0.1
  geocoding: ^3.0.0
  google_maps_flutter: ^2.9.0

  # Image handling
  image_picker: ^1.1.2

  # Utilities
  url_launcher: ^6.3.1
  share_plus: ^10.0.2
  connectivity_plus: ^6.0.5

  # Payment - Simulated (Cashfree integration ready)
  # cashfree_pg: ^2.0.12+32

  # Email notifications - Simulated (EmailJS integration ready)
  # emailjs: ^4.0.0

  # Notifications
  flutter_local_notifications: ^18.0.1

dev_dependencies:
  flutter_test:
    sdk: flutter

flutter:
  uses-material-design: true

  assets:
    - assets/icons/
    - assets/images/
    - assets/logo/

flutter_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/icons/appicon.png"
  min_sdk_android: 23
