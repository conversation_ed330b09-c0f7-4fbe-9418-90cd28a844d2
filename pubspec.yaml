name: hotel_booking
description: One Touch - Hotel Booking App

publish_to: 'none' # Remove this line if you wish to publish to pub.dev

version: 1.0.0+1

environment:
  sdk: ">=3.0.0 <4.0.0"

dependencies:
  flutter:
    sdk: flutter

  cupertino_icons: ^1.0.2
  flutter_svg: ^2.0.10+1
  cached_network_image: ^3.1.0
  badges: ^3.1.2
  carousel_slider: ^5.1.1
  image_stack: ^2.1.1
  firebase_core: ^3.15.1
  firebase_auth: ^5.6.2
  cloud_firestore: ^5.6.11
  path_provider: ^2.1.5
  firebase_crashlytics: ^4.3.9
  firebase_analytics: ^11.5.2
  flutter_launcher_icons: ^0.13.1

dev_dependencies:
  flutter_test:
    sdk: flutter

flutter:
  uses-material-design: true

  assets:
    - assets/icons/

flutter_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/icons/appicon.png"
  min_sdk_android: 23
